# Building Orchestrators Guide

## What This Document Is For

Learn how to build coordination layers that orchestrate WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator to create flexible newsletter generation pipelines.

## What You'll Accomplish

- Understand ADK-native orchestration patterns
- B<PERSON> ResearchPlanner, DynamicResearchCoordinator, and NewsletterSynthesizer
- Implement quality assessment with tool-based escalation
- Create flexible pipelines that work with any newsletter type

## Prerequisites

- Completed [SETUP.md](SETUP.md) and [EXISTING_AGENTS.md](EXISTING_AGENTS.md)
- Understanding of WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator capabilities
- Basic Google ADK knowledge (SequentialAgent, ParallelAgent, LoopAgent)

## 🚨 CRITICAL ANTI-PATTERN WARNINGS

**READ THIS FIRST**: These are the most common violations of our agentic principles that MUST be avoided:

### ❌ ANTI-PATTERN 1: Hardcoded Dictionary Return Structures

**NEVER create tools or functions that return predetermined dictionary structures:**

```python
# ❌ WRONG - Hardcoded dictionary violates agentic principles
def assess_quality(research_results: str) -> dict:
    return {
        "status": "sufficient",  # Hardcoded keys
        "quality_level": "high", # Predetermined structure
        "score": 8.5            # Numerical scoring
    }
```

**✅ CORRECT - Trust LLM intelligence completely:**

```python
# ✅ CORRECT - LLM provides natural language assessment
async def assess_quality_agentic(user_requirements: str, research_results: str) -> str:
    prompt = f"Evaluate this research against user requirements: {user_requirements}..."
    llm_response = await llm.agenerate([{"role": "user", "content": prompt}])
    return llm_response.generations[0][0].text  # Raw LLM assessment
```

### ❌ ANTI-PATTERN 2: Context Isolation

**NEVER create agents that ignore session state context:**

```python
# ❌ WRONG - Agent operates in isolation
class IsolatedAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # Ignores user_requirements and research_plan from session state
        return "generic response"
```

**✅ CORRECT - Proper context propagation:**

```python
# ✅ CORRECT - Agent reads and validates session state
class ContextAwareAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        user_requirements = ctx.session.state.get("user_requirements", "")
        if not user_requirements:
            raise ValueError("No user requirements found for agent execution")
        # Use context in agent logic...
```

### ❌ ANTI-PATTERN 3: Agent Duplication

**NEVER create new agents that duplicate existing functionality:**

```python
# ❌ WRONG - Duplicates WideSearchAgent functionality
class NewsResearchAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research news topics...",  # Duplicates existing capability
            tools=[exa_wide_search]
        )
```

**✅ CORRECT - Orchestrate existing agents:**

```python
# ✅ CORRECT - Use existing WideSearchAgent through orchestration
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent

# Orchestrate, don't duplicate
research_pipeline = SequentialAgent(sub_agents=[wide_search_agent, ...])
```

### ❌ ANTI-PATTERN 4: Silent Fallback Handling

**NEVER use fallback values or silent error handling:**

```python
# ❌ WRONG - Silent fallback masks problems
user_requirements = ctx.session.state.get("user_requirements", "default requirements")
research_plan = ctx.session.state.get("research_plan", "generic plan")
```

**✅ CORRECT - Explicit error handling:**

```python
# ✅ CORRECT - Fail fast with clear errors
user_requirements = ctx.session.state.get("user_requirements", "")
if not user_requirements:
    raise ValueError("No user requirements found for research coordination")
```

## 🚨 MANDATORY IMPLEMENTATION VALIDATION CHECKLIST

**BEFORE writing ANY code, validate your approach against these checklists:**

### Pre-Implementation Anti-Pattern Check

**Run this checklist BEFORE starting any implementation:**

- [ ] **Agentic Intelligence Check**: Does this trust LLM judgment over programmatic logic?
- [ ] **Context Propagation Check**: Does this properly read and validate session state?
- [ ] **Orchestration Check**: Does this use existing agents instead of duplicating functionality?
- [ ] **Error Handling Check**: Does this raise explicit errors instead of silent fallbacks?

**If ANY answer is "no", STOP and redesign your approach.**

### Tool/Function Implementation Validation

**For ANY tool or function you create:**

- [ ] **No Hardcoded Returns**: Does this avoid predetermined dictionary structures like `{"status": "...", "quality_level": "..."}`?
- [ ] **LLM Intelligence**: Does this return raw LLM responses instead of parsed/structured data?
- [ ] **Natural Language**: Does this work with natural language input/output?
- [ ] **Universal Flexibility**: Does this work for ANY newsletter type without modification?

### Agent Implementation Validation

**For ANY agent you create:**

- [ ] **Context Awareness**: Does this read user_requirements and research_plan from session state?
- [ ] **Explicit Validation**: Does this raise ValueError for missing required inputs?
- [ ] **No Duplication**: Does this orchestrate existing agents instead of rebuilding functionality?
- [ ] **Agentic Principles**: Does this trust LLM intelligence over programmatic decision trees?

### Quality Assessment Validation

**For ANY quality assessment implementation:**

- [ ] **LLM-Driven**: Does this use LLM judgment instead of numerical scoring?
- [ ] **Natural Language**: Does this return LLM's raw assessment without parsing?
- [ ] **User-Specific**: Does this evaluate against user's stated requirements?
- [ ] **No Fixed Criteria**: Does this avoid hardcoded quality standards?

## Core Orchestration Strategy

### 🚨 CRITICAL: Universal Flexibility Requirements

**Before implementing ANY agent or workflow**, validate against these requirements:

#### Agent Instruction Validation

- [ ] Does this work for daily news newsletters?
- [ ] Does this work for biotech VC reports?
- [ ] Does this work for fintech regulatory updates?
- [ ] Does this work for academic research summaries?
- [ ] Does this work for any newsletter type I can imagine?

**If any answer is "no", the instruction is too specific and must be made more generic.**

#### Universal Quality Assessment Requirements

- [ ] Does quality assessment adapt to user's stated requirements?
- [ ] Are quality criteria based on user expectations, not hardcoded standards?
- [ ] Can the same quality assessment work for any newsletter type?

### 🚨 MANDATORY: Error Handling Requirements

**ALL agent implementations MUST follow these four critical principles:**

#### 1. Explicit Error Handling

- [ ] **REQUIRED**: Raise clear, descriptive errors when required inputs are missing
- [ ] **REQUIRED**: Use appropriate exception types (ValueError, TypeError, etc.)
- [ ] **FORBIDDEN**: Creating fallback values or proceeding with incomplete data

```python
# ✅ CORRECT - Explicit error handling
if not user_requirements:
    raise ValueError("No user requirements found for research coordination")

# ❌ WRONG - Silent fallback that masks problems
user_requirements = user_requirements or "default requirements"
```

#### 2. Fail Fast Principle

- [ ] **REQUIRED**: Validate critical inputs immediately at start of execution
- [ ] **REQUIRED**: Fail early with clear error messages
- [ ] **FORBIDDEN**: Attempting to continue with degraded functionality

```python
# ✅ CORRECT - Validate inputs first
async def _run_async_impl(self, ctx):
    # Validate ALL critical inputs first
    user_requirements = ctx.session.state.get("user_requirements", "")
    research_plan = ctx.session.state.get("research_plan", "")

    if not user_requirements:
        raise ValueError("No user requirements found for research coordination")
    if not research_plan:
        raise ValueError("No research plan found for research coordination")

    # Only proceed if all inputs are valid
    # ... rest of implementation
```

#### 3. Clear Debugging

- [ ] **REQUIRED**: Error messages must be specific and actionable
- [ ] **REQUIRED**: Clearly indicate what is missing and what user needs to provide
- [ ] **FORBIDDEN**: Generic error messages like "Invalid input"

```python
# ✅ CORRECT - Specific, actionable error messages
raise ValueError("No user requirements found for research coordination")
raise ValueError("No research plan found for research coordination")

# ❌ WRONG - Generic, unhelpful messages
raise ValueError("Invalid input")
raise Exception("Something went wrong")
```

#### 4. Better User Experience

- [ ] **REQUIRED**: Force proper configuration through validation
- [ ] **REQUIRED**: Prevent silent acceptance of incomplete inputs
- [ ] **FORBIDDEN**: Producing poor quality outputs due to missing inputs

**Error Handling Validation Checklist:**

- [ ] Does this agent validate ALL required inputs at the start?
- [ ] Do error messages clearly explain what's missing?
- [ ] Would a developer immediately know how to fix the error?
- [ ] Does this prevent poor quality outputs from incomplete data?

## Main Pipeline Architecture

### Flexible Newsletter Pipeline

```python
from google.adk.agents import SequentialAgent, LlmAgent
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent
from underlines_adk.tools.exa_tools import exa_wide_search

def create_flexible_newsletter_pipeline() -> SequentialAgent:
    """ADK-native pipeline that handles ANY newsletter type using WideSearchAgent and DeepSearchAgent"""

    return SequentialAgent(
        name="FlexibleNewsletterPipeline",
        sub_agents=[
            # Phase 1: Research Planning
            LlmAgent(
                name="ResearchPlanner",
                instruction="""
                Read the user's natural language newsletter requirements carefully.
                Plan research approach based on their specific needs.
                Use exa_wide_search to discover relevant topics and get initial coverage.
                Work directly with their prose requirements - do not extract structured data.
                """,
                tools=[exa_wide_search],
                output_key="research_plan"
            ),

            # Phase 2: Dynamic Research Coordination
            DynamicResearchCoordinator(
                name="FlexibleResearchCoordinator",
                description="Coordinates research using WideSearchAgent and DeepSearchAgent"
            ),

            # Phase 3: Newsletter Synthesis
            LlmAgent(
                name="FlexibleNewsletterSynthesizer",
                instruction="""
                Synthesize research into newsletter format based on user's requirements.
                Follow their specified structure, style, and formatting preferences.
                Ensure quality matches their stated expectations.
                """,
                output_key="final_newsletter"
            )
        ]
    )
```

## Dynamic Research Coordinator

### Custom BaseAgent Implementation

```python
from google.adk.agents import BaseAgent, ParallelAgent, LoopAgent, LlmAgent
from google.adk.core import Event
from google.genai import types
from typing import AsyncGenerator
import os

class DynamicResearchCoordinator(BaseAgent):
    """Orchestrates WideSearchAgent and DeepSearchAgent instances"""

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        # MANDATORY: Validate ALL critical inputs first (Fail Fast Principle)
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_plan = ctx.session.state.get("research_plan", "")

        # MANDATORY: Explicit Error Handling - raise clear errors for missing inputs
        if not user_requirements:
            raise ValueError("No user requirements found for research coordination")
        if not research_plan:
            raise ValueError("No research plan found for research coordination")

        # Create research workflows using WideSearchAgent and DeepSearchAgent
        research_workflows = self.create_research_workflows_from_requirements(
            user_requirements, research_plan
        )

        # Create and run ParallelAgent for concurrent research using WideSearchAgent and DeepSearchAgent
        parallel_researcher = ParallelAgent(
            name="FlexibleConcurrentResearch",
            sub_agents=research_workflows
        )

        async for event in parallel_researcher.run_async(ctx):
            yield event

    def create_research_workflows_from_requirements(self, requirements: str, plan: str) -> list:
        """Create workflows using WideSearchAgent and DeepSearchAgent instances"""
        # Analyze requirements and create appropriate research workflows
        # Use WideSearchAgent and DeepSearchAgent through LoopAgent wrappers for quality enhancement
        return [
            self.create_quality_enhanced_research_loop(f"research_area_{i}", requirements, plan)
            for i in range(self.determine_research_areas_needed(requirements))
        ]
    
    def determine_research_areas_needed(self, requirements: str) -> int:
        """Analyze user requirements to determine number of research areas"""
        # Simple heuristic - can be made more sophisticated
        if "comprehensive" in requirements.lower() or "detailed" in requirements.lower():
            return 3
        elif "brief" in requirements.lower() or "summary" in requirements.lower():
            return 1
        else:
            return 2
    
    def create_quality_enhanced_research_loop(self, area_name: str, user_requirements: str, research_plan: str) -> LoopAgent:
        """Create LoopAgent wrapper around WideSearchAgent and DeepSearchAgent with agentic quality assessment"""

        # Create agentic quality assessment agent for this research area
        quality_agent = QualityAssessmentAgent(
            name=f"AgenticQualityAssessor_{area_name}",
            description=f"Evaluates research quality for {area_name} using LLM intelligence"
        )

        return LoopAgent(
            name=f"QualityEnhancedResearch_{area_name}",
            sub_agents=[
                wide_search_agent,  # Use WideSearchAgent instance
                deep_search_agent,  # Use DeepSearchAgent instance
                quality_agent       # Agentic quality assessment with escalation
            ],
            max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
        )
```

## Agentic Quality Assessment

### 🚨 CRITICAL: Quality Assessment Anti-Patterns to Avoid

**The following approaches VIOLATE agentic principles and were specifically fixed in our implementation:**

#### ❌ ANTI-PATTERN: Hardcoded Dictionary Returns

```python
# ❌ WRONG - This was the old approach that violated agentic principles
def assess_quality_old_way(research_results: str) -> dict:
    return {
        "status": "sufficient",      # Hardcoded keys
        "quality_level": "high",     # Predetermined structure
        "score": 8.5,               # Numerical scoring
        "meets_standards": True      # Boolean flags
    }
```

#### ❌ ANTI-PATTERN: Context Isolation

```python
# ❌ WRONG - Quality assessment that ignores user requirements
class IsolatedQualityAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # Ignores user_requirements from session state
        return {"status": "sufficient"}  # Generic assessment
```

### ✅ CORRECT: Agentic Quality Assessment Implementation

**This is the CORRECT approach that trusts LLM intelligence completely:**

```python
from google.adk.agents import BaseAgent
from google.adk.events import Event, EventActions
from underlines_adk.tools.litellm_tools import llm
from typing import AsyncGenerator

class QualityAssessmentAgent(BaseAgent):
    """Agentic quality assessment that trusts LLM intelligence completely"""

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        # MANDATORY: Validate required inputs with explicit error handling
        user_requirements = ctx.session.state.get("user_requirements", "")

        # Accumulate research results from both agents
        wide_search_results = ctx.session.state.get("wide_search_results", "")
        deep_search_results = ctx.session.state.get("deep_search_results", "")

        # Combine all available research results
        research_results_parts = []
        if wide_search_results:
            research_results_parts.append(f"Wide Search Results:\n{wide_search_results}")
        if deep_search_results:
            research_results_parts.append(f"Deep Search Results:\n{deep_search_results}")

        research_results = "\n\n".join(research_results_parts)

        # MANDATORY: Explicit error handling for missing inputs
        if not user_requirements:
            raise ValueError("No user requirements found for quality assessment")
        if not research_results:
            raise ValueError("No research results found for quality assessment")

        # Construct LLM prompt for quality evaluation
        assessment_prompt = f"""
        Evaluate this research quality against the user's specific requirements.

        USER REQUIREMENTS:
        {user_requirements}

        RESEARCH RESULTS:
        {research_results}

        EVALUATION TASK:
        Use your professional judgment to determine if this research meets the user's
        expectations for their specific newsletter type. Consider:

        - Does this meet their specified depth and coverage?
        - Are sources credible and relevant to their domain?
        - Is the analysis appropriate for their stated audience?
        - Does this match their quality expectations?

        RESPONSE FORMAT:
        Start your response with either "SUFFICIENT" or "NEEDS_IMPROVEMENT" followed
        by your detailed reasoning. Use your professional judgment - no numerical
        scoring needed.
        """

        # Use LLM to evaluate quality in natural language
        try:
            llm_response = await llm.agenerate(
                messages=[{"role": "user", "content": assessment_prompt}],
                max_tokens=int(os.getenv("MAX_QUALITY_ASSESSMENT_TOKENS", "1000"))
            )

            # Extract the LLM's assessment (RAW, no parsing)
            llm_assessment = llm_response.generations[0][0].text.strip()

        except Exception as e:
            raise ValueError(f"Failed to evaluate research quality: {str(e)}")

        # Simple pass/fail determination based on LLM's natural language response
        # Trust the LLM's judgment completely - no complex parsing
        is_sufficient = llm_assessment.upper().startswith("SUFFICIENT")

        # Save LLM's RAW assessment and escalate based on LLM judgment
        yield Event(
            author=self.name,
            actions=EventActions(
                state_delta={"quality_assessment": llm_assessment},  # Raw LLM response
                escalate=is_sufficient  # Escalate (terminate loop) if quality is sufficient
            )
        )
```

### Agentic Quality Assessment Pattern

```python
# Quality assessment that trusts LLM intelligence completely
quality_assessment_agent = QualityAssessmentAgent(
    name="AgenticQualityAssessor",
    description="Evaluates research quality using LLM intelligence and natural language assessment"
)

# Use in LoopAgent for iterative quality improvement
quality_enhanced_research = LoopAgent(
    name="QualityEnhancedResearch",
    sub_agents=[
        wide_search_agent,        # Existing WideSearchAgent
        deep_search_agent,        # Existing DeepSearchAgent
        quality_assessment_agent  # Agentic quality assessment with escalation
    ],
    max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
)
```

## Session State Management

### 🚨 MANDATORY: Context Propagation Patterns

**ALL agents MUST implement proper context propagation following these patterns:**

#### Pattern 1: Context Validation (MANDATORY)

```python
# MANDATORY: Validate ALL required inputs at start of _run_async_impl
async def _run_async_impl(self, ctx):
    # Read required context from session state
    user_requirements = ctx.session.state.get("user_requirements", "")
    research_plan = ctx.session.state.get("research_plan", "")

    # MANDATORY: Explicit error handling for missing inputs
    if not user_requirements:
        raise ValueError("No user requirements found for research coordination")
    if not research_plan:
        raise ValueError("No research plan found for research coordination")

    # Only proceed with valid context
    # ... rest of implementation
```

#### Pattern 2: Context Injection for Existing Agents

```python
# Context-aware wrapper that injects context before running existing agents
class ContextAwareAgentWrapper(BaseAgent):
    def __init__(self, wrapped_agent, user_requirements, research_plan, focus_area):
        super().__init__(name=f"ContextAware{wrapped_agent.name}")
        self.wrapped_agent = wrapped_agent
        self.user_requirements = user_requirements
        self.research_plan = research_plan
        self.focus_area = focus_area

    async def _run_async_impl(self, ctx):
        # MANDATORY: Inject context into session state before running wrapped agent
        context_injection = {
            "user_requirements": self.user_requirements,
            "research_plan": self.research_plan,
            "current_focus": self.focus_area,
            "context_injected_by": self.name
        }

        # Update session state with context
        yield Event(
            author=self.name,
            actions=EventActions(state_delta=context_injection)
        )

        # Now run the wrapped agent with proper context
        async for event in self.wrapped_agent.run_async(ctx):
            yield event
```

### ADK-Native State Patterns

```python
# Automatic state persistence via output_key
research_planner = LlmAgent(
    name="ResearchPlanner",
    instruction="Plan research based on user requirements",
    output_key="research_plan"  # ADK automatically saves final response
)

# State consumption by subsequent agents with MANDATORY validation
coordinator = DynamicResearchCoordinator(
    name="ResearchCoordinator"
    # MUST validate user_requirements and research_plan in _run_async_impl
)

# Manual state updates using EventActions (advanced pattern)
from google.adk.events import Event, EventActions
import time

async def update_state_manually(ctx, key, value):
    """Update session state manually with proper event handling"""
    state_changes = {key: value}
    actions = EventActions(state_delta=state_changes)

    event = Event(
        invocation_id=f"state_update_{key}",
        author="system",
        actions=actions,
        timestamp=time.time()
    )

    # Yield the event - ADK will commit the state changes
    yield event

# State scope management (ADK pattern)
def set_scoped_state(ctx, key, value, scope="session"):
    """Set state with proper scope prefix"""
    if scope == "user":
        scoped_key = f"user:{key}"
    elif scope == "app":
        scoped_key = f"app:{key}"
    elif scope == "temp":
        scoped_key = f"temp:{key}"
    else:
        scoped_key = key  # session scope (default)

    ctx.session.state[scoped_key] = value
    return scoped_key

# Safe state access with validation (MANDATORY pattern)
def get_required_state(ctx, key, error_message=None):
    """Get required state with proper error handling"""
    value = ctx.session.state.get(key, "")
    if not value:
        error_msg = error_message or f"Required state key '{key}' not found"
        raise ValueError(error_msg)
    return value
```

### State Flow Example

1. **User Input**: Natural language requirements stored in `session.state["user_requirements"]`
2. **ResearchPlanner**: Saves results to `session.state["research_plan"]` via `output_key`
3. **DynamicResearchCoordinator**: Reads plan from session state, validates inputs with explicit error handling
4. **Parallel Research**: Each research loop saves results to `session.state[f"research_area_{i}"]`
5. **Quality Assessment**: Saves quality evaluations to `session.state[f"quality_assessment_{i}"]`
6. **NewsletterSynthesizer**: Reads all research results for synthesis, saves final output via `output_key`

**State Validation Pattern** (MANDATORY):

```python
async def _run_async_impl(self, ctx):
    # MANDATORY: Validate ALL required inputs first
    user_requirements = get_required_state(ctx, "user_requirements",
                                          "No user requirements found for research coordination")
    research_plan = get_required_state(ctx, "research_plan",
                                     "No research plan found for research coordination")

    # Only proceed with valid inputs
    # ... rest of implementation
```

## Environment Configuration

### Safety Limits

```bash
# ADK Safety Limits (configurable)
MAX_RESEARCH_ITERATIONS=3          # LoopAgent max_iterations
MAX_PARALLEL_TOPICS=5              # ParallelAgent concurrency
MAX_QUALITY_ASSESSMENT_TOKENS=1000 # LLM token limits

# Quality Assessment Configuration
QUALITY_ASSESSMENT_TIMEOUT=60      # Seconds for quality evaluation
RESEARCH_COORDINATION_TIMEOUT=300  # Seconds for full research coordination
```

### Safety Philosophy

- **Safety, Not Constraints**: Limits prevent runaway costs, don't constrain creativity
- **LLM Intelligence**: Quality decisions made by LLM judgment, not numerical thresholds
- **Tool-Based Control**: Use ADK's tool escalation for loop termination
- **Configurable Limits**: All safety limits adjustable via environment variables

## Testing Your Orchestrators

### Basic Orchestration Test

```python
import asyncio
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner
from google.genai import types

async def test_newsletter_pipeline():
    # Create pipeline
    pipeline = create_flexible_newsletter_pipeline()
    
    # Setup session
    session_service = LoggedSessionService()
    runner = Runner(
        agent=pipeline,
        app_name="newsletter_test",
        session_service=session_service
    )
    
    # Test with natural language requirements
    user_requirements = """
    I need a weekly technology newsletter focusing on AI developments 
    and their business implications. Include 3-4 major stories with 
    analysis of market impact and competitive landscape.
    """
    
    user_content = types.Content(
        parts=[types.Part(text=user_requirements)]
    )
    
    # Run pipeline
    async for event in runner.run_async(
        user_id="test_user",
        session_id="test_session",
        new_message=user_content
    ):
        if event.is_final_response():
            print("Newsletter generated successfully!")
            print(event.content.parts[0].text[:500] + "...")
            break

# Run test
asyncio.run(test_newsletter_pipeline())
```

## Common Orchestration Patterns

### Pattern 1: Sequential Research → Synthesis

```python
SequentialAgent([
    research_planner,
    research_coordinator,
    newsletter_synthesizer
])
```

### Pattern 2: Parallel Research → Quality Assessment

```python
ParallelAgent([
    wide_search_agent,
    deep_search_agent,
    quality_assessor
])
```

### Pattern 3: Iterative Quality Improvement

```python
LoopAgent([
    researcher,
    quality_assessor,  # with exit_research_loop tool
    content_improver
])
```

## Next Steps

- **Test your orchestrators**: Use the testing patterns above
- **Debug issues**: Consult [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for common problems
- **Deploy**: Follow deployment guidance in TROUBLESHOOTING.md

## 🚨 MANDATORY PRINCIPLES AND ANTI-PATTERN PREVENTION

### Core Principles (MUST Follow)

1. **Orchestrate, don't duplicate**: Use WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator through coordination
2. **Universal flexibility**: Every agent must work for any newsletter type
3. **LLM-driven quality**: Use tool-based escalation, not programmatic thresholds
4. **Natural language requirements**: Work directly with user prose, no structured extraction
5. **🚨 MANDATORY Error Handling**: All agents must validate inputs and raise clear errors for missing data

### Anti-Patterns That MUST Be Avoided

1. **❌ NEVER use hardcoded dictionary return structures** like `{"status": "...", "quality_level": "..."}`
2. **❌ NEVER create agents that ignore session state context** - always validate user_requirements and research_plan
3. **❌ NEVER duplicate existing agent functionality** - orchestrate WideSearchAgent and DeepSearchAgent instead
4. **❌ NEVER use silent fallback handling** - raise explicit ValueError for missing inputs
5. **❌ NEVER parse LLM responses into predetermined structures** - trust LLM intelligence completely

### Validation Requirements

**Before implementing ANY code, validate against:**

- [ ] Does this trust LLM intelligence over programmatic logic?
- [ ] Does this properly propagate context through session state?
- [ ] Does this orchestrate existing agents instead of duplicating functionality?
- [ ] Does this raise explicit errors for missing inputs?
- [ ] Does this work universally for any newsletter type?

**If ANY answer is "no", STOP and redesign your approach.**

### Reference Implementation

**For complete examples of CORRECT implementations that avoid all anti-patterns:**

- See `underlines_adk/agents/dynamic_research_coordinator.py` for ADK-native coordination patterns (simplified from overengineered version)
- See `underlines_adk/agents/quality_assessment_agent.py` for agentic quality assessment
- See `examples/test_agentic_quality_assessment.py` for validation testing patterns

**IMPORTANT**: The DynamicResearchCoordinator was recently refactored to eliminate overengineering anti-patterns. The current implementation demonstrates proper ADK-native patterns - no custom wrappers, direct ParallelAgent coordination, and trust in ADK's session state management.
