# Building Orchestrators Guide

## What This Document Is For

Learn how to build coordination layers that orchestrate WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator to create flexible newsletter generation pipelines.

## What You'll Accomplish

- Understand ADK-native orchestration patterns
- <PERSON><PERSON> ResearchPlanner, DynamicResearchCoordinator, and NewsletterSynthesizer
- Implement quality assessment with tool-based escalation
- Create flexible pipelines that work with any newsletter type

## Prerequisites

- Completed [SETUP.md](SETUP.md) and [EXISTING_AGENTS.md](EXISTING_AGENTS.md)
- Understanding of WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator capabilities
- Basic Google ADK knowledge (SequentialAgent, ParallelAgent, LoopAgent)

## 🚨 CRITICAL: Anti-Pattern Prevention

**BEFORE implementing ANY code, read [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md) for comprehensive anti-pattern prevention guidelines.**

**This document focuses on practical implementation patterns. The anti-patterns guide provides detailed validation checklists and examples of what NOT to do.**

## Implementation Validation

**Use the comprehensive validation checklists in [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md) before implementing any code.**

## Core Orchestration Strategy

### 🚨 CRITICAL: Universal Flexibility Requirements

**Before implementing ANY agent or workflow**, validate against these requirements:

#### Agent Instruction Validation

- [ ] Does this work for daily news newsletters?
- [ ] Does this work for biotech VC reports?
- [ ] Does this work for fintech regulatory updates?
- [ ] Does this work for academic research summaries?
- [ ] Does this work for any newsletter type I can imagine?

**If any answer is "no", the instruction is too specific and must be made more generic.**

#### Universal Quality Assessment Requirements

- [ ] Does quality assessment adapt to user's stated requirements?
- [ ] Are quality criteria based on user expectations, not hardcoded standards?
- [ ] Can the same quality assessment work for any newsletter type?

### Error Handling Requirements

**ALL agents must implement explicit error handling. See [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md) for comprehensive error handling patterns and validation requirements.**

## Main Pipeline Architecture

### Flexible Newsletter Pipeline

```python
from google.adk.agents import SequentialAgent, LlmAgent
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent
from underlines_adk.tools.exa_tools import exa_wide_search

def create_flexible_newsletter_pipeline() -> SequentialAgent:
    """ADK-native pipeline that handles ANY newsletter type using WideSearchAgent and DeepSearchAgent"""

    return SequentialAgent(
        name="FlexibleNewsletterPipeline",
        sub_agents=[
            # Phase 1: Research Planning
            LlmAgent(
                name="ResearchPlanner",
                instruction="""
                Read the user's natural language newsletter requirements carefully.
                Plan research approach based on their specific needs.
                Use exa_wide_search to discover relevant topics and get initial coverage.
                Work directly with their prose requirements - do not extract structured data.
                """,
                tools=[exa_wide_search],
                output_key="research_plan"
            ),

            # Phase 2: Dynamic Research Coordination
            DynamicResearchCoordinator(
                name="FlexibleResearchCoordinator",
                description="Coordinates research using WideSearchAgent and DeepSearchAgent"
            ),

            # Phase 3: Newsletter Synthesis
            LlmAgent(
                name="FlexibleNewsletterSynthesizer",
                instruction="""
                Synthesize research into newsletter format based on user's requirements.
                Follow their specified structure, style, and formatting preferences.
                Ensure quality matches their stated expectations.
                """,
                output_key="final_newsletter"
            )
        ]
    )
```

## Dynamic Research Coordinator

### Custom BaseAgent Implementation

```python
from google.adk.agents import BaseAgent, ParallelAgent, LoopAgent, LlmAgent
from google.adk.core import Event
from google.genai import types
from typing import AsyncGenerator
import os

class DynamicResearchCoordinator(BaseAgent):
    """Orchestrates WideSearchAgent and DeepSearchAgent instances"""

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        # MANDATORY: Validate ALL critical inputs first (Fail Fast Principle)
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_plan = ctx.session.state.get("research_plan", "")

        # MANDATORY: Explicit Error Handling - raise clear errors for missing inputs
        if not user_requirements:
            raise ValueError("No user requirements found for research coordination")
        if not research_plan:
            raise ValueError("No research plan found for research coordination")

        # Create research workflows using WideSearchAgent and DeepSearchAgent
        research_workflows = self.create_research_workflows_from_requirements(
            user_requirements, research_plan
        )

        # Create and run ParallelAgent for concurrent research using WideSearchAgent and DeepSearchAgent
        parallel_researcher = ParallelAgent(
            name="FlexibleConcurrentResearch",
            sub_agents=research_workflows
        )

        async for event in parallel_researcher.run_async(ctx):
            yield event

    def create_research_workflows_from_requirements(self, requirements: str, plan: str) -> list:
        """Create workflows using WideSearchAgent and DeepSearchAgent instances"""
        # Analyze requirements and create appropriate research workflows
        # Use WideSearchAgent and DeepSearchAgent through LoopAgent wrappers for quality enhancement
        return [
            self.create_quality_enhanced_research_loop(f"research_area_{i}", requirements, plan)
            for i in range(self.determine_research_areas_needed(requirements))
        ]
    
    def determine_research_areas_needed(self, requirements: str) -> int:
        """Analyze user requirements to determine number of research areas"""
        # Simple heuristic - can be made more sophisticated
        if "comprehensive" in requirements.lower() or "detailed" in requirements.lower():
            return 3
        elif "brief" in requirements.lower() or "summary" in requirements.lower():
            return 1
        else:
            return 2
    
    def create_quality_enhanced_research_loop(self, area_name: str, user_requirements: str, research_plan: str) -> LoopAgent:
        """Create LoopAgent wrapper around WideSearchAgent and DeepSearchAgent with agentic quality assessment"""

        # Create agentic quality assessment agent for this research area
        quality_agent = QualityAssessmentAgent(
            name=f"AgenticQualityAssessor_{area_name}",
            description=f"Evaluates research quality for {area_name} using LLM intelligence"
        )

        return LoopAgent(
            name=f"QualityEnhancedResearch_{area_name}",
            sub_agents=[
                wide_search_agent,  # Use WideSearchAgent instance
                deep_search_agent,  # Use DeepSearchAgent instance
                quality_agent       # Agentic quality assessment with escalation
            ],
            max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
        )
```

## Agentic Quality Assessment

### Implementation Pattern

**Use LLM intelligence for quality assessment. See [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md) for anti-patterns to avoid.**

```python
from google.adk.agents import BaseAgent
from google.adk.events import Event, EventActions
from underlines_adk.tools.litellm_tools import llm
from typing import AsyncGenerator

class QualityAssessmentAgent(BaseAgent):
    """Agentic quality assessment that trusts LLM intelligence completely"""

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        # MANDATORY: Validate required inputs with explicit error handling
        user_requirements = ctx.session.state.get("user_requirements", "")

        # Accumulate research results from both agents
        wide_search_results = ctx.session.state.get("wide_search_results", "")
        deep_search_results = ctx.session.state.get("deep_search_results", "")

        # Combine all available research results
        research_results_parts = []
        if wide_search_results:
            research_results_parts.append(f"Wide Search Results:\n{wide_search_results}")
        if deep_search_results:
            research_results_parts.append(f"Deep Search Results:\n{deep_search_results}")

        research_results = "\n\n".join(research_results_parts)

        # MANDATORY: Explicit error handling for missing inputs
        if not user_requirements:
            raise ValueError("No user requirements found for quality assessment")
        if not research_results:
            raise ValueError("No research results found for quality assessment")

        # Construct LLM prompt for quality evaluation
        assessment_prompt = f"""
        Evaluate this research quality against the user's specific requirements.

        USER REQUIREMENTS:
        {user_requirements}

        RESEARCH RESULTS:
        {research_results}

        EVALUATION TASK:
        Use your professional judgment to determine if this research meets the user's
        expectations for their specific newsletter type. Consider:

        - Does this meet their specified depth and coverage?
        - Are sources credible and relevant to their domain?
        - Is the analysis appropriate for their stated audience?
        - Does this match their quality expectations?

        RESPONSE FORMAT:
        Start your response with either "SUFFICIENT" or "NEEDS_IMPROVEMENT" followed
        by your detailed reasoning. Use your professional judgment - no numerical
        scoring needed.
        """

        # Use LLM to evaluate quality in natural language
        try:
            llm_response = await llm.agenerate(
                messages=[{"role": "user", "content": assessment_prompt}],
                max_tokens=int(os.getenv("MAX_QUALITY_ASSESSMENT_TOKENS", "1000"))
            )

            # Extract the LLM's assessment (RAW, no parsing)
            llm_assessment = llm_response.generations[0][0].text.strip()

        except Exception as e:
            raise ValueError(f"Failed to evaluate research quality: {str(e)}")

        # Simple pass/fail determination based on LLM's natural language response
        # Trust the LLM's judgment completely - no complex parsing
        is_sufficient = llm_assessment.upper().startswith("SUFFICIENT")

        # Save LLM's RAW assessment and escalate based on LLM judgment
        yield Event(
            author=self.name,
            actions=EventActions(
                state_delta={"quality_assessment": llm_assessment},  # Raw LLM response
                escalate=is_sufficient  # Escalate (terminate loop) if quality is sufficient
            )
        )
```

### Agentic Quality Assessment Pattern

```python
# Quality assessment that trusts LLM intelligence completely
quality_assessment_agent = QualityAssessmentAgent(
    name="AgenticQualityAssessor",
    description="Evaluates research quality using LLM intelligence and natural language assessment"
)

# Use in LoopAgent for iterative quality improvement
quality_enhanced_research = LoopAgent(
    name="QualityEnhancedResearch",
    sub_agents=[
        wide_search_agent,        # Existing WideSearchAgent
        deep_search_agent,        # Existing DeepSearchAgent
        quality_assessment_agent  # Agentic quality assessment with escalation
    ],
    max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
)
```

## Session State Management

### Context Propagation Patterns

**Key Patterns:**

1. **Context Validation**: Always validate required inputs with explicit error handling
2. **Context Injection**: Use EventActions to inject context before running existing agents
3. **State Persistence**: Use `output_key` for automatic state saving
4. **State Access**: Use safe access patterns with validation

**Detailed patterns and examples available in [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md).**

### State Flow

1. User Input → `session.state["user_requirements"]`
2. ResearchPlanner → `session.state["research_plan"]` (via output_key)
3. DynamicResearchCoordinator → validates inputs, coordinates research
4. Quality Assessment → `session.state["quality_assessment"]`
5. NewsletterSynthesizer → reads all results, produces final output

## Environment Configuration

### Safety Limits

```bash
# ADK Safety Limits (configurable)
MAX_RESEARCH_ITERATIONS=3          # LoopAgent max_iterations
MAX_PARALLEL_TOPICS=5              # ParallelAgent concurrency
MAX_QUALITY_ASSESSMENT_TOKENS=1000 # LLM token limits

# Quality Assessment Configuration
QUALITY_ASSESSMENT_TIMEOUT=60      # Seconds for quality evaluation
RESEARCH_COORDINATION_TIMEOUT=300  # Seconds for full research coordination
```

### Safety Philosophy

- **Safety, Not Constraints**: Limits prevent runaway costs, don't constrain creativity
- **LLM Intelligence**: Quality decisions made by LLM judgment, not numerical thresholds
- **Tool-Based Control**: Use ADK's tool escalation for loop termination
- **Configurable Limits**: All safety limits adjustable via environment variables

## Testing Your Orchestrators

### Basic Orchestration Test

```python
import asyncio
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner
from google.genai import types

async def test_newsletter_pipeline():
    # Create pipeline
    pipeline = create_flexible_newsletter_pipeline()
    
    # Setup session
    session_service = LoggedSessionService()
    runner = Runner(
        agent=pipeline,
        app_name="newsletter_test",
        session_service=session_service
    )
    
    # Test with natural language requirements
    user_requirements = """
    I need a weekly technology newsletter focusing on AI developments 
    and their business implications. Include 3-4 major stories with 
    analysis of market impact and competitive landscape.
    """
    
    user_content = types.Content(
        parts=[types.Part(text=user_requirements)]
    )
    
    # Run pipeline
    async for event in runner.run_async(
        user_id="test_user",
        session_id="test_session",
        new_message=user_content
    ):
        if event.is_final_response():
            print("Newsletter generated successfully!")
            print(event.content.parts[0].text[:500] + "...")
            break

# Run test
asyncio.run(test_newsletter_pipeline())
```

## Common Orchestration Patterns

### Pattern 1: Sequential Research → Synthesis

```python
SequentialAgent([
    research_planner,
    research_coordinator,
    newsletter_synthesizer
])
```

### Pattern 2: Parallel Research → Quality Assessment

```python
ParallelAgent([
    wide_search_agent,
    deep_search_agent,
    quality_assessor
])
```

### Pattern 3: Iterative Quality Improvement

```python
LoopAgent([
    researcher,
    quality_assessor,  # with exit_research_loop tool
    content_improver
])
```

## Cross-References and Navigation

### Related Documentation

**Prerequisites:**

- [SETUP.md](SETUP.md) - Environment setup and basic validation
- [EXISTING_AGENTS.md](EXISTING_AGENTS.md) - Agent capabilities and integration strategy

**Implementation Support:**

- [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md) - **CRITICAL**: Comprehensive anti-pattern prevention and validation checklists
- [TROUBLESHOOTING.md](TROUBLESHOOTING.md) - Debugging and error resolution

**Project Foundation:**

- [PRD.md](PRD.md) - Product vision and agentic principles

### Quick Reference

**Key Sections in This Document:**

- Lines 26-28: Implementation validation requirements
- Lines 30-54: Error handling requirements
- Lines 56-120: Main pipeline architecture
- Lines 122-180: Dynamic research coordinator implementation
- Lines 183-187: Agentic quality assessment patterns
- Lines 294-313: Session state management
- Lines 315-340: Environment configuration
- Lines 342-389: Testing patterns

## Next Steps

- **Test your orchestrators**: Use the testing patterns above
- **Debug issues**: Consult [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for common problems
- **Prevent anti-patterns**: Review [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md) before implementing
