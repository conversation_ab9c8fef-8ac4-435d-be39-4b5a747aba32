# Agent Capabilities Reference

## What This Document Is For

Understand the capabilities and flexibility of our WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator before building new orchestration layers.

## What You'll Accomplish

- Learn what research capabilities already exist
- Understand why these agents are perfect for flexible newsletter generation
- See how to use these agents in your orchestration code
- Understand the integration strategy (orchestration vs duplication)

## Prerequisites

- Completed [SETUP.md](SETUP.md) and have agents working
- Basic understanding of Google ADK agent patterns

## 🚨 CRITICAL: "Orchestrate, Don't Duplicate" Principle

### ✅ Strategic Decision

**Use WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator through orchestration layers - NO duplication or modification needed.**

**Why This Approach**:

- ✅ **These agents are already perfect**: Work with any topic/query without hardcoded newsletter-specific logic
- ✅ **Proven quality**: WideSearchAgent and DeepSearchAgent have been tested and provide excellent results
- ✅ **Backward compatibility**: No changes to existing functionality
- ✅ **Minimal development effort**: Focus on orchestration, not rebuilding research capabilities

### 🚨 ANTI-PATTERN WARNING: Agent Duplication

**The following approaches VIOLATE our core principles and MUST be avoided:**

#### ❌ NEVER Create Newsletter-Specific Agents

```python
# ❌ WRONG - Violates "Orchestrate, don't duplicate" principle
class DailyNewsAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research daily news topics...",  # Duplicates WideSearchAgent
            tools=[exa_wide_search]
        )

class BiotechVCAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research biotech companies...",  # Duplicates DeepSearchAgent
            tools=[exa_deep_search]
        )
```

**Why This Is Wrong:**

- Duplicates existing proven functionality
- Breaks backward compatibility
- Creates maintenance burden
- Violates universal flexibility principle

#### ❌ NEVER Modify Existing Agent Instructions

```python
# ❌ WRONG - Modifying existing agents breaks backward compatibility
wide_search_agent.instruction = "Focus only on technology news..."  # DON'T DO THIS
deep_search_agent.tools = [custom_tool]  # DON'T DO THIS
```

**Why This Is Wrong:**

- Breaks existing workflows that depend on these agents
- Violates universal flexibility principle
- Creates unpredictable behavior

#### ✅ CORRECT Approach: Context-Aware Orchestration

```python
# ✅ CORRECT - Use existing agents through context-aware orchestration
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent

class ContextAwareAgentWrapper(BaseAgent):
    def __init__(self, wrapped_agent, user_requirements, research_plan):
        super().__init__(name=f"ContextAware{wrapped_agent.name}")
        self.wrapped_agent = wrapped_agent
        self.user_requirements = user_requirements
        self.research_plan = research_plan

    async def _run_async_impl(self, ctx):
        # Inject context into session state
        yield Event(actions=EventActions(state_delta={
            "user_requirements": self.user_requirements,
            "research_plan": self.research_plan
        }))

        # Run existing agent with proper context
        async for event in self.wrapped_agent.run_async(ctx):
            yield event
```

## Integration Strategy: Orchestration Over Duplication

## WideSearchAgent

### Purpose & Capabilities

**Perfect for**: Broad topic discovery, comprehensive coverage, initial research phase

### Key Strengths

- **Universal flexibility**: Works with any topic or query without modification
- **Comprehensive coverage**: Returns 30 results with highlights for broad understanding
- **ADK-native**: Proper LlmAgent implementation with tools and output_key
- **No hardcoded logic**: Instruction works for any newsletter type

### Usage Example

```python
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent

# Use directly in orchestration
wide_search_result = await wide_search_agent.run_async(ctx)
# Returns structured stories with titles, URLs, summaries, reasoning, dates
```

### Output Format

```json
[
  {
    "title": "Title of the story",
    "reference_urls": ["url1", "url2", "url3"],
    "summary": "Summary of the story",
    "reasoning": "Reasoning for why this story is relevant to the topic",
    "date": "Best estimate for the date of the story"
  }
]
```

### When to Use WideSearchAgent

- Initial topic discovery for any newsletter type
- Broad coverage of current events
- Finding trending stories in a domain
- Getting comprehensive overview before deep analysis

### Flexibility Examples

- **Daily News**: "Latest global politics and technology news"
- **Biotech VC**: "Recent TL1A research and company developments"
- **Fintech**: "Digital banking innovations and regulatory updates"
- **Academic**: "Machine learning research publications this month"

## DeepSearchAgent

### Purpose & Capabilities

**Perfect for**: Detailed analysis, in-depth research, comprehensive fact-checking

### Key Strengths

- **Universal applicability**: Works for any domain or newsletter type
- **Comprehensive analysis**: Full text content with extensive citations
- **Professional quality**: Extensive citation requirements and multi-perspective analysis
- **ADK-native**: Proper LlmAgent implementation with tools and output_key

### Usage Example

```python
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent

# Use directly in orchestration
deep_analysis = await deep_search_agent.run_async(ctx)
# Returns detailed analysis with extensive citations
```

### Output Characteristics

- **Comprehensive background**: Historical context and foundational information
- **Multiple perspectives**: Balanced analysis from different viewpoints
- **Extensive citations**: Professional-quality source attribution
- **Detailed analysis**: In-depth examination appropriate for the topic

### When to Use DeepSearchAgent

- Detailed analysis of specific stories or topics
- Fact-checking and verification
- Historical context and background research
- Multi-perspective analysis for complex issues

### Flexibility Examples

- **Daily News**: Deep analysis of major political developments
- **Biotech VC**: Comprehensive analysis of clinical trial results
- **Fintech**: Detailed regulatory impact analysis
- **Academic**: In-depth review of research methodologies

## ResearchPlanner

### Purpose & Capabilities

**Perfect for**: Orchestrating research workflows, analyzing natural language requirements, planning coordination strategies

### Key Strengths

- **Natural language analysis**: Works directly with user prose requirements without structured data extraction
- **Dynamic planning**: Creates research approaches based on specific user needs
- **Agent orchestration**: Plans coordination of WideSearchAgent and DeepSearchAgent
- **Universal flexibility**: Adapts to any newsletter type or research requirement

### Usage Example

```python
from underlines_adk.agents.research_planner import research_planner

# Use directly in orchestration
research_plan = await research_planner.run_async(ctx)
# Returns research strategy for coordinating other agents
```

### When to Use ResearchPlanner

- Initial analysis of natural language newsletter requirements
- Planning research workflows for complex projects
- Determining coordination strategies for multiple agents
- Topic discovery and research scope definition

## DynamicResearchCoordinator

### Purpose & Capabilities

**Perfect for**: LLM-driven coordination of multiple agents, dynamic workflow execution, agentic orchestration

### Key Strengths

- **LLM intelligence**: Uses AI judgment instead of programmatic logic for coordination decisions
- **Dynamic workflows**: Creates ParallelAgent patterns based on requirements
- **Agent coordination**: Orchestrates WideSearchAgent and DeepSearchAgent execution
- **Agentic principles**: Trusts LLM intelligence over hardcoded validation rules
- **Quality enhancement**: Integrates with QualityAssessmentAgent for iterative improvement

### Usage Example

```python
from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator

# ADK-native coordination (simplified implementation)
coordinator = DynamicResearchCoordinator()
coordination_results = await coordinator.run_async(ctx)
# Returns coordinated research from multiple agents using direct ParallelAgent coordination

# Quality-enhanced research with LoopAgent
research_loop = coordinator.create_quality_enhanced_research_loop(area_name="biotech")
# Returns LoopAgent with existing agents + quality assessment
```

**Note**: The DynamicResearchCoordinator was recently refactored to use ADK-native patterns, eliminating unnecessary ContextAwareAgentWrapper complexity in favor of direct ParallelAgent coordination.

### When to Use DynamicResearchCoordinator

- Coordinating multiple research agents simultaneously
- Implementing LLM-driven workflow decisions
- Managing complex research coordination strategies
- Executing parallel research workflows

## QualityAssessmentAgent

### Purpose & Capabilities

**Perfect for**: Agentic quality evaluation using LLM intelligence, ADK-native loop termination, natural language assessment

### Key Strengths

- **LLM-driven evaluation**: Uses AI judgment for quality assessment in natural language
- **ADK-native escalation**: Proper Event(actions=EventActions(escalate=True)) pattern
- **Universal flexibility**: Adapts to any newsletter type without hardcoded criteria
- **Agentic principles**: Trusts LLM intelligence completely, no predetermined structures
- **Explicit error handling**: Clear ValueError exceptions for missing inputs

### Usage Example

```python
from underlines_adk.agents.quality_assessment_agent import QualityAssessmentAgent
from google.adk.agents import LoopAgent

# Create agentic quality assessment
quality_agent = QualityAssessmentAgent(
    name="AgenticQualityAssessor",
    description="Evaluates research quality using LLM intelligence"
)

# Use in LoopAgent for iterative improvement
loop_agent = LoopAgent(
    sub_agents=[research_agent, quality_agent],
    max_iterations=3
)
```

### When to Use QualityAssessmentAgent

- Evaluating research quality against user requirements
- Implementing iterative quality improvement loops
- Terminating LoopAgent execution when quality is sufficient
- Providing natural language quality feedback

## Underlying Tools

### exa_wide_search

- **Purpose**: Comprehensive topic coverage
- **Configuration**: 30 results maximum, highlights only
- **Strategy**: Breadth over depth
- **Use Case**: Topic exploration, news gathering, trend analysis

### exa_deep_search

- **Purpose**: Detailed content analysis
- **Configuration**: 5 results maximum, full text content
- **Strategy**: Depth over breadth
- **Use Case**: Detailed research, fact-checking, comprehensive analysis

## How to Use in Orchestration

### Direct Usage Pattern

```python
# Use agents directly in your orchestration
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent
from underlines_adk.agents.research_planner import research_planner
from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator

# In your orchestration logic
async def coordinate_research(ctx, user_requirements):
    # 🚨 MANDATORY: Validate inputs first (Fail Fast Principle)
    if not user_requirements:
        raise ValueError("No user requirements provided for research coordination")

    # Use WideSearchAgent for broad coverage
    wide_results = await wide_search_agent.run_async(ctx)

    # Use DeepSearchAgent for detailed analysis
    deep_analysis = await deep_search_agent.run_async(ctx)

    return combine_results(wide_results, deep_analysis)
```

### ParallelAgent Coordination

```python
from google.adk.agents import ParallelAgent

# Create multiple instances for concurrent research
parallel_researcher = ParallelAgent(
    name="ConcurrentResearch",
    sub_agents=[
        wide_search_agent,  # WideSearchAgent instance
        deep_search_agent,  # DeepSearchAgent instance
        # Add more instances as needed
    ]
)
```

### LoopAgent Quality Enhancement

```python
from google.adk.agents import LoopAgent

# Wrap WideSearchAgent and DeepSearchAgent with quality assessment
quality_enhanced_research = LoopAgent(
    name="QualityEnhancedResearch",
    sub_agents=[
        wide_search_agent,    # WideSearchAgent instance
        deep_search_agent,    # DeepSearchAgent instance
        quality_assessor      # Your quality assessment agent
    ],
    max_iterations=3
)
```

## 🚨 CRITICAL ANTI-PATTERNS TO AVOID

### ❌ ANTI-PATTERN 1: Hardcoded Quality Assessment

**NEVER create quality assessment tools with predetermined return structures:**

```python
# ❌ WRONG - Hardcoded dictionary violates agentic principles
def assess_research_quality(research_results: str) -> dict:
    return {
        "status": "sufficient",     # Hardcoded keys
        "quality_level": "high",    # Predetermined structure
        "score": 8.5,              # Numerical scoring
        "meets_standards": True     # Boolean flags
    }
```

**✅ CORRECT - Trust LLM intelligence completely:**

```python
# ✅ CORRECT - LLM provides natural language assessment
class QualityAssessmentAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # Get LLM's natural language assessment
        llm_response = await llm.agenerate([{"role": "user", "content": prompt}])
        assessment = llm_response.generations[0][0].text

        # Simple pass/fail based on LLM's judgment
        is_sufficient = assessment.upper().startswith("SUFFICIENT")

        yield Event(actions=EventActions(
            state_delta={"quality_assessment": assessment},  # Raw LLM response
            escalate=is_sufficient
        ))
```

### ❌ ANTI-PATTERN 2: Context Isolation in Quality Assessment

**NEVER create quality assessment that ignores user requirements:**

```python
# ❌ WRONG - Generic quality assessment ignores user context
def generic_quality_check(research_results: str) -> bool:
    # Uses hardcoded criteria, ignores user requirements
    return len(research_results) > 1000  # Arbitrary threshold
```

**✅ CORRECT - Context-aware quality assessment:**

```python
# ✅ CORRECT - Evaluates against user's specific requirements
async def _run_async_impl(self, ctx):
    user_requirements = ctx.session.state.get("user_requirements", "")
    if not user_requirements:
        raise ValueError("No user requirements found for quality assessment")

    # LLM evaluates against user's specific needs
    prompt = f"Evaluate research against these requirements: {user_requirements}..."
```

### ❌ ANTI-PATTERN 3: Agent Duplication

**NEVER create new agents that duplicate existing functionality:**

```python
# ❌ WRONG - Creating new research agents
class NewsResearchAgent(LlmAgent):  # Don't do this!
    # This duplicates WideSearchAgent functionality

# ✅ CORRECT - Use WideSearchAgent
wide_search_agent  # Use this instead
```

### ❌ ANTI-PATTERN 4: Silent Context Failures

**NEVER use fallback values when context is missing:**

```python
# ❌ WRONG - Silent fallback masks problems
user_requirements = ctx.session.state.get("user_requirements", "default requirements")

# ✅ CORRECT - Explicit error handling
user_requirements = ctx.session.state.get("user_requirements", "")
if not user_requirements:
    raise ValueError("No user requirements found for agent execution")
```

## Quality Assessment

### Existing Agent Quality Standards

Both agents already provide:

- **Professional citation standards**: Proper source attribution
- **Factual accuracy**: Comprehensive fact-checking
- **Bias awareness**: Multiple perspective analysis
- **Source credibility**: Quality source selection

### Integration Quality Pattern

When building orchestration layers:

1. **Use existing agent outputs**: Don't rebuild research capabilities
2. **Add coordination logic**: Focus on workflow orchestration
3. **Enhance with quality assessment**: Add user-specific quality evaluation
4. **Maintain flexibility**: Ensure orchestration works for any newsletter type
5. **🚨 MANDATORY Error Handling**: Validate all inputs and raise clear errors for missing data

## Next Steps

Now that you understand existing capabilities:

1. **Start building orchestration**: Follow [BUILDING_ORCHESTRATORS.md](BUILDING_ORCHESTRATORS.md) to create coordination layers
2. **Get help with issues**: Consult [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for debugging guidance

## Key Takeaway

Our WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator are already perfectly designed for flexible newsletter generation. Your job is to build orchestration layers that coordinate these proven agents, not to replace or duplicate their excellent research capabilities.

## 🚨 CRITICAL: Anti-Pattern Prevention

**BEFORE implementing ANY code, read [ANTI_PATTERNS_AND_VALIDATION.md](ANTI_PATTERNS_AND_VALIDATION.md) to understand:**

- Specific anti-patterns that MUST be avoided
- Validation checklists to run before implementation
- Examples of violations and their correct alternatives
- Context propagation and error handling requirements

**The anti-patterns documented there were specifically identified and fixed in our recent implementations. Following those guidelines will prevent you from repeating the same mistakes.**
